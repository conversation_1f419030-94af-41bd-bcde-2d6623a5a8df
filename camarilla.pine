strategy(title="CamarillaStrategy", shorttitle="Camarilla", overlay=true)

EMA = ema(close,8)

//Camarilla
h4 = close + (high - low) * 1.1 / 2.0
l4 = close - (high - low) * 1.1 / 2.0

//Daily Pivots
dtime_h4 = security(tickerid, 'D', h4[1])
dtime_l4 = security(tickerid, 'D', l4[1])

// --- Plotting ---
plot(EMA, color=green, linewidth=2, title="EMA 8")
plot(showPivots ? dtime_h4 : na, color=orange, linewidth=2, title="Daily H4 (Resistance)")
plot(showPivots ? dtime_l4 : na, color=orange, linewidth=2, title="Daily L4 (Support)")

longCondition = close >dtime_h4 and open < dtime_h4 and EMA < close
if (longCondition)
    strategy.entry("Long", strategy.long)
    strategy.exit ("Exit Long","Long",  trail_points = 40,trail_offset = 1, loss =70)

shortCondition = close <dtime_l4 and open >dtime_l4 and EMA > close
if (shortCondition)
    strategy.entry("Short", strategy.short)
    strategy.exit ("Exit Short","Short", trail_points = 20,trail_offset = 1, loss =40)

